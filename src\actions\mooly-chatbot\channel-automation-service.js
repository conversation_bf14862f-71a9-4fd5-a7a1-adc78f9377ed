'use client';

import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'channel_automation_followup';

/**
 * L<PERSON>y cấu hình automation follow-up cho channel
 * @param {string} channelId - ID của channel
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChannelAutomationConfig(channelId) {
  if (!channelId) {
    return { success: false, error: 'Channel ID is required', data: null };
  }

  try {
    const result = await fetchData(TABLE_NAME, {
      filters: { channelId },
      single: true
    });

    // Xử lý lỗi PGRST116 khi không có dữ liệu - pattern từ workflow-config-service
    if (!result.success && (result.error?.code === 'NO_ROWS_OR_MULTIPLE_ROWS' || result.error?.code === 'PGRST116')) {
      return { success: true, data: null, error: null };
    }

    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * <PERSON><PERSON><PERSON> danh sách cấu hình automation follow-up với các tùy chọn lọc
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChannelAutomationConfigs(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Tạo hoặc cập nhật cấu hình automation follow-up cho channel
 * @param {string} channelId - ID của channel
 * @param {Object} configData - Dữ liệu cấu hình
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertChannelAutomationConfig(channelId, configData) {
  if (!channelId) {
    return { success: false, error: 'Channel ID is required', data: null };
  }

  const data = {
    channelId,
    ...configData
  };

  // Sử dụng unique constraint (tenant_id, channel_id)
  return upsertData(TABLE_NAME, data, ['tenantId', 'channelId']);
}

/**
 * Cập nhật cấu hình automation follow-up
 * @param {string} configId - ID của cấu hình
 * @param {Object} configData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateChannelAutomationConfig(configId, configData) {
  if (!configId) {
    return { success: false, error: 'Config ID is required', data: null };
  }

  return updateData(TABLE_NAME, configData, { id: configId });
}

/**
 * Xóa cấu hình automation follow-up
 * @param {string} configId - ID của cấu hình
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteChannelAutomationConfig(configId) {
  if (!configId) {
    return { success: false, error: 'Config ID is required', data: null };
  }

  return deleteData(TABLE_NAME, { id: configId });
}

/**
 * Bật/tắt automation follow-up cho channel
 * @param {string} channelId - ID của channel
 * @param {boolean} isEnabled - Trạng thái bật/tắt
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function toggleChannelAutomation(channelId, isEnabled) {
  if (!channelId) {
    return { success: false, error: 'Channel ID is required', data: null };
  }

  try {
    // Kiểm tra xem đã có cấu hình chưa
    const existingConfig = await getChannelAutomationConfig(channelId);

    if (existingConfig.success && existingConfig.data) {
      // Cập nhật cấu hình hiện có
      return updateChannelAutomationConfig(existingConfig.data.id, { isEnabled });
    } else if (existingConfig.success && !existingConfig.data) {
      // Chưa có cấu hình - tạo mới
      return upsertChannelAutomationConfig(channelId, {
        isEnabled,
        followupRules: []
      });
    } else {
      // Có lỗi khi kiểm tra
      return existingConfig;
    }
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Utility functions để xử lý followup rules
 */

/**
 * Tạo rule mới với ID duy nhất
 * @param {number} delayMinutes - Thời gian delay (phút)
 * @param {string} message - Nội dung tin nhắn
 * @param {number} order - Thứ tự rule
 * @returns {Object} - Rule object
 */
export function createFollowupRule(delayMinutes, message, order = 1) {
  return {
    id: `rule_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    delayMinutes: parseInt(delayMinutes, 10),
    message: message.trim(),
    isEnabled: true,
    order: parseInt(order, 10),
    createdAt: new Date().toISOString()
  };
}

/**
 * Validate followup rule
 * @param {Object} rule - Rule object
 * @returns {Object} - Validation result
 */
export function validateFollowupRule(rule) {
  const errors = [];

  if (!rule.delayMinutes || rule.delayMinutes < 1) {
    errors.push('Thời gian delay phải lớn hơn 0 phút');
  }

  if (rule.delayMinutes > 43200) { // 30 days in minutes
    errors.push('Thời gian delay không được vượt quá 30 ngày');
  }

  if (!rule.message || rule.message.trim().length === 0) {
    errors.push('Nội dung tin nhắn không được để trống');
  }

  if (rule.message && rule.message.length > 2000) {
    errors.push('Nội dung tin nhắn không được vượt quá 2000 ký tự');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sắp xếp rules theo thứ tự
 * @param {Array} rules - Mảng rules
 * @returns {Array} - Mảng rules đã sắp xếp
 */
export function sortFollowupRules(rules) {
  return [...rules].sort((a, b) => {
    // Sắp xếp theo order trước, sau đó theo delayMinutes
    if (a.order !== b.order) {
      return a.order - b.order;
    }
    return a.delayMinutes - b.delayMinutes;
  });
}

/**
 * Format thời gian delay thành text dễ đọc
 * @param {number} minutes - Số phút
 * @returns {string} - Text format
 */
export function formatDelayTime(minutes) {
  if (minutes < 60) {
    return `${minutes} phút`;
  } else if (minutes < 1440) { // < 24 hours
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return `${hours} giờ`;
    }
    return `${hours} giờ ${remainingMinutes} phút`;
  } else {
    const days = Math.floor(minutes / 1440);
    const remainingHours = Math.floor((minutes % 1440) / 60);
    if (remainingHours === 0) {
      return `${days} ngày`;
    }
    return `${days} ngày ${remainingHours} giờ`;
  }
}

/**
 * Preset thời gian delay phổ biến
 */
export const DELAY_PRESETS = [
  { value: 5, label: '5 phút' },
  { value: 10, label: '10 phút' },
  { value: 15, label: '15 phút' },
  { value: 30, label: '30 phút' },
  { value: 60, label: '1 giờ' },
  { value: 120, label: '2 giờ' },
  { value: 180, label: '3 giờ' },
  { value: 300, label: '5 giờ' },
  { value: 480, label: '8 giờ' },
  { value: 720, label: '12 giờ' },
  { value: 1440, label: '1 ngày' },
  { value: 2880, label: '2 ngày' },
  { value: 4320, label: '3 ngày' },
  { value: 10080, label: '1 tuần' }
];

/**
 * Template tin nhắn mẫu
 */
export const MESSAGE_TEMPLATES = [
  'Xin chào! Bạn có cần hỗ trợ gì thêm không?',
  'Chúng tôi vẫn sẵn sàng hỗ trợ bạn. Hãy liên hệ nếu cần!',
  'Bạn có muốn tìm hiểu thêm về sản phẩm/dịch vụ của chúng tôi?',
  'Cảm ơn bạn đã quan tâm! Chúng tôi có thể giúp gì cho bạn?',
  'Bạn có câu hỏi nào khác mà chúng tôi có thể hỗ trợ không?'
];
