'use client';

import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Switch from '@mui/material/Switch';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import FormControlLabel from '@mui/material/FormControlLabel';
import { alpha, useTheme } from '@mui/material/styles';

import { LoadingButton } from '@mui/lab';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import {
  getChannelAutomationConfig,
  upsertChannelAutomationConfig,
  createFollowupRule,
  validateFollowupRule,
  sortFollowupRules,
  formatDelayTime,
  DELAY_PRESETS,
  MESSAGE_TEMPLATES
} from 'src/actions/mooly-chatbot/channel-automation-service';

// ----------------------------------------------------------------------

export default function ChannelAutomationDialog({ open, onClose, channel }) {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  
  // State cho cấu hình
  const [isEnabled, setIsEnabled] = useState(true);
  const [followupRules, setFollowupRules] = useState([]);
  
  // State cho form thêm rule mới
  const [showAddForm, setShowAddForm] = useState(false);
  const [newRule, setNewRule] = useState({
    delayMinutes: 5,
    message: '',
    customDelay: false
  });

  // Load cấu hình hiện tại khi mở dialog
  useEffect(() => {
    if (open && channel?.connection?.id) {
      loadAutomationConfig();
    }
  }, [open, channel?.connection?.id]);

  // Load cấu hình automation
  const loadAutomationConfig = async () => {
    setLoading(true);
    try {
      const channelId = channel?.connection?.id;
      if (!channelId) {
        toast.error('Không tìm thấy ID channel');
        return;
      }

      const result = await getChannelAutomationConfig(channelId);
      if (result.success && result.data) {
        setIsEnabled(result.data.isEnabled || false);
        setFollowupRules(sortFollowupRules(result.data.followupRules || []));
      } else {
        // Cấu hình mặc định
        setIsEnabled(true);
        setFollowupRules([]);
      }
    } catch (error) {
      console.error('Error loading automation config:', error);
      toast.error('Không thể tải cấu hình automation');
    } finally {
      setLoading(false);
    }
  };

  // Lưu cấu hình
  const handleSave = async () => {
    setSaving(true);
    try {
      const channelId = channel?.connection?.id;
      if (!channelId) {
        toast.error('Không tìm thấy ID channel');
        return;
      }

      const result = await upsertChannelAutomationConfig(channelId, {
        isEnabled,
        followupRules: sortFollowupRules(followupRules)
      });

      if (result.success) {
        toast.success('Lưu cấu hình automation thành công!');
        onClose();
      } else {
        toast.error(`Lỗi: ${result.error}`);
      }
    } catch (error) {
      console.error('Error saving automation config:', error);
      toast.error('Không thể lưu cấu hình automation');
    } finally {
      setSaving(false);
    }
  };

  // Thêm rule mới
  const handleAddRule = () => {
    const validation = validateFollowupRule(newRule);
    if (!validation.isValid) {
      toast.error(validation.errors[0]);
      return;
    }

    const rule = createFollowupRule(
      newRule.delayMinutes,
      newRule.message,
      followupRules.length + 1
    );

    setFollowupRules(prev => sortFollowupRules([...prev, rule]));
    setNewRule({ delayMinutes: 5, message: '', customDelay: false });
    setShowAddForm(false);
    toast.success('Thêm quy tắc thành công!');
  };

  // Xóa rule
  const handleDeleteRule = (ruleId) => {
    setFollowupRules(prev => prev.filter(rule => rule.id !== ruleId));
    toast.success('Xóa quy tắc thành công!');
  };

  // Toggle rule
  const handleToggleRule = (ruleId) => {
    setFollowupRules(prev =>
      prev.map(rule =>
        rule.id === ruleId ? { ...rule, isEnabled: !rule.isEnabled } : rule
      )
    );
  };

  // Sử dụng template tin nhắn
  const handleUseTemplate = (template) => {
    setNewRule(prev => ({ ...prev, message: template }));
  };

  // Reset form khi đóng dialog
  const handleClose = () => {
    setShowAddForm(false);
    setNewRule({ delayMinutes: 5, message: '', customDelay: false });
    onClose();
  };

  if (!channel) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: { height: '80vh' }
        }
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:settings-bold-duotone" width={24} />
          <Box>
            <Typography variant="h6">Cấu hình Automation Follow-up</Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {channel.name}
            </Typography>
          </Box>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ pb: 1 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Typography>Đang tải cấu hình...</Typography>
          </Box>
        ) : (
          <Stack spacing={3}>
            {/* Bật/tắt automation */}
            <Card sx={{ p: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isEnabled}
                    onChange={(e) => setIsEnabled(e.target.checked)}
                    color="success"
                  />
                }
                label={
                  <Box>
                    <Typography variant="subtitle2">
                      Bật tính năng Automation Follow-up
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Tự động gửi tin nhắn bám đuổi khách hàng sau khoảng thời gian không tương tác
                    </Typography>
                  </Box>
                }
              />
            </Card>

            {/* Danh sách rules */}
            <Box>
              <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6">Quy tắc Follow-up</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<Iconify icon="eva:plus-fill" />}
                  onClick={() => setShowAddForm(true)}
                  disabled={!isEnabled}
                >
                  Thêm quy tắc
                </Button>
              </Stack>

              {followupRules.length === 0 ? (
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Chưa có quy tắc nào. Hãy thêm quy tắc đầu tiên!
                  </Typography>
                </Card>
              ) : (
                <Stack spacing={2}>
                  {followupRules.map((rule, index) => (
                    <Card
                      key={rule.id}
                      sx={{
                        p: 2,
                        opacity: rule.isEnabled ? 1 : 0.6,
                        border: rule.isEnabled ? `1px solid ${alpha(theme.palette.success.main, 0.3)}` : undefined
                      }}
                    >
                      <Stack direction="row" alignItems="flex-start" spacing={2}>
                        <Chip
                          label={index + 1}
                          size="small"
                          color="primary"
                          sx={{ mt: 0.5 }}
                        />
                        
                        <Box flexGrow={1}>
                          <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                            <Chip
                              label={formatDelayTime(rule.delayMinutes)}
                              size="small"
                              variant="outlined"
                              color="info"
                            />
                            <Switch
                              size="small"
                              checked={rule.isEnabled}
                              onChange={() => handleToggleRule(rule.id)}
                              disabled={!isEnabled}
                            />
                          </Stack>
                          
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {rule.message}
                          </Typography>
                        </Box>

                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteRule(rule.id)}
                        >
                          <Iconify icon="eva:trash-2-fill" width={16} />
                        </IconButton>
                      </Stack>
                    </Card>
                  ))}
                </Stack>
              )}
            </Box>

            {/* Form thêm rule mới */}
            {showAddForm && (
              <Card sx={{ p: 2, border: `2px dashed ${theme.palette.primary.main}` }}>
                <Typography variant="subtitle2" mb={2}>
                  Thêm quy tắc mới
                </Typography>
                
                <Stack spacing={2}>
                  {/* Chọn thời gian delay */}
                  <TextField
                    select
                    label="Thời gian delay"
                    value={newRule.customDelay ? 'custom' : newRule.delayMinutes}
                    onChange={(e) => {
                      if (e.target.value === 'custom') {
                        setNewRule(prev => ({ ...prev, customDelay: true }));
                      } else {
                        setNewRule(prev => ({
                          ...prev,
                          delayMinutes: parseInt(e.target.value, 10),
                          customDelay: false
                        }));
                      }
                    }}
                    size="small"
                  >
                    {DELAY_PRESETS.map((preset) => (
                      <MenuItem key={preset.value} value={preset.value}>
                        {preset.label}
                      </MenuItem>
                    ))}
                    <MenuItem value="custom">Tùy chỉnh...</MenuItem>
                  </TextField>

                  {/* Input thời gian tùy chỉnh */}
                  {newRule.customDelay && (
                    <TextField
                      type="number"
                      label="Thời gian delay (phút)"
                      value={newRule.delayMinutes}
                      onChange={(e) => setNewRule(prev => ({
                        ...prev,
                        delayMinutes: parseInt(e.target.value, 10) || 1
                      }))}
                      size="small"
                      slotProps={{
                        htmlInput: { min: 1, max: 43200 }
                      }}
                      helperText="Từ 1 phút đến 30 ngày (43200 phút)"
                    />
                  )}

                  {/* Nội dung tin nhắn */}
                  <TextField
                    multiline
                    rows={3}
                    label="Nội dung tin nhắn"
                    value={newRule.message}
                    onChange={(e) => setNewRule(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Nhập nội dung tin nhắn follow-up..."
                    size="small"
                  />

                  {/* Template tin nhắn */}
                  <Box>
                    <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
                      Hoặc chọn template có sẵn:
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                      {MESSAGE_TEMPLATES.map((template, index) => (
                        <Chip
                          key={index}
                          label={template.length > 30 ? `${template.substring(0, 30)}...` : template}
                          size="small"
                          variant="outlined"
                          onClick={() => handleUseTemplate(template)}
                          sx={{ cursor: 'pointer' }}
                        />
                      ))}
                    </Stack>
                  </Box>

                  {/* Actions */}
                  <Stack direction="row" spacing={1} justifyContent="flex-end">
                    <Button
                      size="small"
                      onClick={() => setShowAddForm(false)}
                    >
                      Hủy
                    </Button>
                    <Button
                      size="small"
                      variant="contained"
                      onClick={handleAddRule}
                      disabled={!newRule.message.trim()}
                    >
                      Thêm
                    </Button>
                  </Stack>
                </Stack>
              </Card>
            )}
          </Stack>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={handleClose}>
          Hủy
        </Button>
        <LoadingButton
          variant="contained"
          onClick={handleSave}
          loading={saving}
          disabled={loading}
        >
          Lưu cấu hình
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}

ChannelAutomationDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  channel: PropTypes.object,
};
